# 🗄️ Dagstart Database Migration Guide

## 📋 Overview
This document provides complete instructions for migrating the Dagstart Supabase database to another PostgreSQL instance or different SQL database system.

## ⚡ Quick Migration (PostgreSQL to PostgreSQL)

### Fast Backup & Restore
```bash
# Create backup (replace password with actual password)
pg_dump -Fc -v -d "postgresql://postgres:#NewAccount1234#@db.dbsztlsxgbheifrpmsaa.supabase.co:5432/postgres" --schema=public -f dagstart_supabase_dump.bak

# Restore to new PostgreSQL instance
pg_restore -v -d "********************************************/new_database" dagstart_supabase_dump.bak
```

## 🔧 Environment Variables Required

```env
# Required for application to work
VITE_SUPABASE_URL=your_new_database_url
VITE_SUPABASE_ANON_KEY=your_new_anon_key

# Optional: Service role key for admin operations
SUPABASE_SERVICE_ROLE_KEY=your_new_service_role_key
```

## 🗄️ Database Schema Overview

### Tables Summary
- **`authorized_managers`** - Manager authorization system
- **`local_storage_data`** - Application data storage (production, history, etc.)
- **`todos`** - Task management with team assignments
- **`notifications`** - User notification system
- **`td_logbook_entries`** - Technical Department logbook
- **`td_reporter_names`** - Reporter names for TD logbook
- **`user_profiles`** - Extended user profile information
- **`user_teams`** - User team assignments
- **`auth_users_view`** - View for accessing auth.users data

### Storage
- **`images` bucket** - Private bucket for todo images (3MB limit)

## 📊 Complete Database Schema

### 1. `authorized_managers` Table
```sql
CREATE TABLE public.authorized_managers (
    user_id UUID NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    PRIMARY KEY (user_id)
);

-- Add foreign key constraint (if auth.users exists)
ALTER TABLE authorized_managers 
ADD CONSTRAINT authorized_managers_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Indexes
CREATE UNIQUE INDEX authorized_managers_pkey ON authorized_managers (user_id);

-- Comments
COMMENT ON TABLE authorized_managers IS 'Stores the user IDs of individuals authorized to access restricted management sections.';
COMMENT ON COLUMN authorized_managers.user_id IS 'References the user''s ID in auth.users.';

-- RLS Policies
ALTER TABLE authorized_managers ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow authenticated users to read their own entry" 
ON authorized_managers FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Allow service_role to manage entries" 
ON authorized_managers FOR ALL 
USING (true);
```

### 2. `local_storage_data` Table
```sql
CREATE TABLE public.local_storage_data (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key TEXT NOT NULL UNIQUE,
    value JSONB NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ
);

-- Indexes
CREATE UNIQUE INDEX local_storage_data_pkey ON local_storage_data (id);
CREATE UNIQUE INDEX local_storage_data_key_key ON local_storage_data (key);

-- RLS Policies
ALTER TABLE local_storage_data ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Enable read access for all" ON local_storage_data FOR SELECT TO public USING (true);
CREATE POLICY "Enable insert access for all" ON local_storage_data FOR INSERT TO public WITH CHECK (true);
CREATE POLICY "Enable update access for all" ON local_storage_data FOR UPDATE TO public USING (true) WITH CHECK (true);
CREATE POLICY "Enable delete access for all" ON local_storage_data FOR DELETE TO public USING (true);
```

### 3. `todos` Table
```sql
CREATE TABLE public.todos (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    priority TEXT CHECK (priority IN ('low', 'medium', 'high')),
    assigned_teams TEXT[], -- Array of team names
    due_date DATE,
    completed BOOLEAN DEFAULT false NOT NULL,
    completed_at TIMESTAMPTZ,
    completed_by UUID,
    created_by UUID DEFAULT auth.uid(),
    comments TEXT,
    images TEXT[], -- Array of image URLs
    expired_notified BOOLEAN DEFAULT false
);

-- Add foreign key constraints (if auth.users exists)
ALTER TABLE todos 
ADD CONSTRAINT todos_completed_by_fkey 
FOREIGN KEY (completed_by) REFERENCES auth.users(id) ON DELETE SET NULL;

ALTER TABLE todos 
ADD CONSTRAINT todos_created_by_fkey 
FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE SET NULL;

-- Indexes
CREATE INDEX idx_todos_created_at ON todos(created_at);
CREATE INDEX idx_todos_completed ON todos(completed);
CREATE INDEX idx_todos_created_by ON todos(created_by);
CREATE INDEX idx_todos_assigned_teams ON todos USING GIN (assigned_teams);

-- RLS Policies
ALTER TABLE todos ENABLE ROW LEVEL SECURITY;

-- Managers can do everything
CREATE POLICY "Allow managers to view all todos" ON todos FOR SELECT 
USING (EXISTS (SELECT 1 FROM authorized_managers WHERE user_id = auth.uid()));

CREATE POLICY "Allow managers to insert todos" ON todos FOR INSERT 
WITH CHECK (EXISTS (SELECT 1 FROM authorized_managers WHERE user_id = auth.uid()));

CREATE POLICY "Allow managers to update any todo" ON todos FOR UPDATE 
USING (EXISTS (SELECT 1 FROM authorized_managers WHERE user_id = auth.uid()));

CREATE POLICY "Allow managers to delete any todo" ON todos FOR DELETE 
USING (EXISTS (SELECT 1 FROM authorized_managers WHERE user_id = auth.uid()));

-- Team members can view and update completion status
CREATE POLICY "Allow users to view todos for their team" ON todos FOR SELECT 
USING (EXISTS (SELECT 1 FROM user_teams WHERE user_id = auth.uid() AND team = ANY(todos.assigned_teams)));

CREATE POLICY "Allow team members to update completion status" ON todos FOR UPDATE
USING (EXISTS (SELECT 1 FROM user_teams WHERE user_id = auth.uid() AND team = ANY(todos.assigned_teams)));
```

### 4. `notifications` Table
```sql
CREATE TABLE public.notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    user_id UUID,
    type TEXT NOT NULL,
    message TEXT NOT NULL,
    related_id TEXT,
    read BOOLEAN NOT NULL DEFAULT false,
    data JSONB
);

-- Add foreign key constraint (if auth.users exists)
ALTER TABLE notifications
ADD CONSTRAINT notifications_user_id_fkey
FOREIGN KEY (user_id) REFERENCES auth.users(id);

-- Indexes
CREATE INDEX notifications_user_id_idx ON notifications(user_id);
CREATE INDEX notifications_read_idx ON notifications(read);

-- RLS Policies (recommended to add)
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own notifications" ON notifications FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON notifications FOR UPDATE
USING (auth.uid() = user_id);
```

### 5. `td_logbook_entries` Table
```sql
CREATE TABLE public.td_logbook_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    timestamp TIMESTAMPTZ NOT NULL DEFAULT now(),
    "shiftColor" TEXT NOT NULL, -- Shift team color
    line TEXT NOT NULL, -- Production line
    equipment TEXT, -- Equipment/machine part
    text TEXT NOT NULL, -- Log message
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    reporter_name TEXT -- Name of person reporting
);

-- RLS Policies
ALTER TABLE td_logbook_entries ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow authenticated read access" ON td_logbook_entries FOR SELECT
TO anon, authenticated USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated insert access" ON td_logbook_entries FOR INSERT
TO anon, authenticated WITH CHECK (auth.role() = 'authenticated');
```

### 6. `td_reporter_names` Table
```sql
CREATE TABLE public.td_reporter_names (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL UNIQUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- RLS Policies
ALTER TABLE td_reporter_names ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow authenticated read access" ON td_reporter_names FOR SELECT
TO authenticated USING (true);

CREATE POLICY "Allow authenticated insert access" ON td_reporter_names FOR INSERT
TO authenticated WITH CHECK (true);
```

### 7. `user_profiles` Table
```sql
CREATE TABLE public.user_profiles (
    user_id UUID NOT NULL PRIMARY KEY,
    display_name TEXT,
    job_title TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Add foreign key constraint (if auth.users exists)
ALTER TABLE user_profiles
ADD CONSTRAINT user_profiles_user_id_fkey
FOREIGN KEY (user_id) REFERENCES auth.users(id);

-- RLS Policies
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own profile" ON user_profiles FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own profile" ON user_profiles FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own profile" ON user_profiles FOR UPDATE
USING (auth.uid() = user_id);
```

### 8. `user_teams` Table
```sql
CREATE TABLE public.user_teams (
    user_id UUID NOT NULL PRIMARY KEY,
    team TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Add foreign key constraint (if auth.users exists)
ALTER TABLE user_teams
ADD CONSTRAINT user_teams_user_id_fkey
FOREIGN KEY (user_id) REFERENCES auth.users(id);

-- RLS Policies
ALTER TABLE user_teams ENABLE ROW LEVEL SECURITY;

-- Users can view their own team
CREATE POLICY "Users can view their own team" ON user_teams FOR SELECT
USING (auth.uid() = user_id);

-- Managers can manage all teams
CREATE POLICY "Managers can view all teams" ON user_teams FOR SELECT
USING (EXISTS (SELECT 1 FROM authorized_managers WHERE user_id = auth.uid()));

CREATE POLICY "Managers can insert teams" ON user_teams FOR INSERT
WITH CHECK (EXISTS (SELECT 1 FROM authorized_managers WHERE user_id = auth.uid()));

CREATE POLICY "Managers can update teams" ON user_teams FOR UPDATE
USING (EXISTS (SELECT 1 FROM authorized_managers WHERE user_id = auth.uid()));
```

### 9. `auth_users_view` (View)
```sql
-- Create view to access auth.users data (if auth schema exists)
CREATE VIEW public.auth_users_view AS
SELECT
    users.id,
    users.email,
    users.created_at,
    users.last_sign_in_at,
    users.email_confirmed_at
FROM auth.users;

-- Grant permissions
GRANT SELECT ON auth_users_view TO authenticated;
```

## 📁 Storage Configuration

### Images Bucket Setup
```sql
-- Create storage bucket (Supabase specific - adapt for other systems)
INSERT INTO storage.buckets (id, name, public, file_size_limit)
VALUES ('images', 'images', false, 3145728); -- 3MB limit

-- Storage Policies
CREATE POLICY "Allow authenticated uploads" ON storage.objects FOR INSERT
TO authenticated WITH CHECK (bucket_id = 'images');

CREATE POLICY "Allow authenticated downloads" ON storage.objects FOR SELECT
TO authenticated USING (bucket_id = 'images');

CREATE POLICY "Allow users to delete own uploads" ON storage.objects FOR DELETE
TO authenticated USING (bucket_id = 'images' AND auth.uid()::text = (storage.foldername(name))[1]);
```

## 🔄 Migration Strategies

### Option 1: PostgreSQL to PostgreSQL (Recommended)
```bash
# 1. Create backup
pg_dump -Fc -v -d "postgresql://postgres:#NewAccount1234#@db.dbsztlsxgbheifrpmsaa.supabase.co:5432/postgres" --schema=public -f dagstart_supabase_dump.bak

# 2. Create new database
createdb -h new-host -U username new_database_name

# 3. Restore backup
pg_restore -v -d "********************************************/new_database_name" dagstart_supabase_dump.bak

# 4. Verify data
psql -h new-host -U username -d new_database_name -c "\dt"
```

### Option 2: PostgreSQL to MySQL/MariaDB
```sql
-- Required modifications for MySQL:

-- 1. Replace UUID with CHAR(36) or use MySQL 8.0+ UUID functions
-- 2. Replace JSONB with JSON
-- 3. Replace TEXT[] arrays with JSON arrays or separate junction tables
-- 4. Replace TIMESTAMPTZ with TIMESTAMP + timezone handling in app
-- 5. Replace gen_random_uuid() with UUID() function
-- 6. Implement RLS logic in application layer

-- Example conversion for todos table:
CREATE TABLE todos (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    priority ENUM('low', 'medium', 'high'),
    assigned_teams JSON, -- Store as JSON array
    due_date DATE,
    completed BOOLEAN DEFAULT false NOT NULL,
    completed_at TIMESTAMP NULL,
    completed_by CHAR(36),
    created_by CHAR(36),
    comments TEXT,
    images JSON, -- Store as JSON array
    expired_notified BOOLEAN DEFAULT false
);
```

### Option 3: PostgreSQL to SQL Server
```sql
-- Required modifications for SQL Server:

-- 1. Replace UUID with UNIQUEIDENTIFIER
-- 2. Replace JSONB with NVARCHAR(MAX) + JSON constraints
-- 3. Replace TEXT[] with separate junction tables or JSON
-- 4. Replace gen_random_uuid() with NEWID()
-- 5. Replace now() with GETUTCDATE()

-- Example conversion for todos table:
CREATE TABLE todos (
    id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    created_at DATETIME2 DEFAULT GETUTCDATE() NOT NULL,
    title NVARCHAR(MAX) NOT NULL,
    description NVARCHAR(MAX),
    priority NVARCHAR(10) CHECK (priority IN ('low', 'medium', 'high')),
    assigned_teams NVARCHAR(MAX) CHECK (ISJSON(assigned_teams) = 1),
    due_date DATE,
    completed BIT DEFAULT 0 NOT NULL,
    completed_at DATETIME2 NULL,
    completed_by UNIQUEIDENTIFIER,
    created_by UNIQUEIDENTIFIER,
    comments NVARCHAR(MAX),
    images NVARCHAR(MAX) CHECK (ISJSON(images) = 1),
    expired_notified BIT DEFAULT 0
);
```

## 📊 Critical Data to Migrate

### Key Data Stored in `local_storage_data`
```sql
-- Production data keys to verify after migration:
SELECT key FROM local_storage_data WHERE key IN (
    'productionData',           -- Current production data for all lines
    'historyData',             -- Historical production reports
    'vkHistoryData',           -- V&K (Quality Control) history
    'disruptionHistoryData',   -- Disruption/downtime history
    'equipmentOptionsData',    -- Equipment configuration per line
    'productionTargets',       -- Production targets per line
    'productionYieldTargets',  -- Yield targets per line
    'independentDisruptionsData', -- Independent disruption entries
    'logbookEntriesData',      -- Logbook entries
    'logbookHistoryData'       -- Historical logbook data
);
```

## ⚙️ Post-Migration Configuration

### 1. Authentication Setup
```sql
-- Add initial authorized managers (replace with actual user IDs)
INSERT INTO authorized_managers (user_id) VALUES
('your-manager-user-id-1'),
('your-manager-user-id-2');

-- Verify auth system is working
SELECT * FROM auth_users_view LIMIT 5;
```

### 2. Application Configuration Updates
```env
# Update these environment variables in your application:
VITE_SUPABASE_URL=your_new_database_url
VITE_SUPABASE_ANON_KEY=your_new_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_new_service_role_key
```

### 3. Storage Migration (if using file storage)
```bash
# Download all images from Supabase storage
# Upload to new storage system
# Update image URLs in todos.images arrays
```

## 🔍 Verification Checklist

### Data Integrity Checks
```sql
-- 1. Verify table counts
SELECT
    'authorized_managers' as table_name, COUNT(*) as row_count FROM authorized_managers
UNION ALL
SELECT 'local_storage_data', COUNT(*) FROM local_storage_data
UNION ALL
SELECT 'todos', COUNT(*) FROM todos
UNION ALL
SELECT 'notifications', COUNT(*) FROM notifications
UNION ALL
SELECT 'td_logbook_entries', COUNT(*) FROM td_logbook_entries
UNION ALL
SELECT 'td_reporter_names', COUNT(*) FROM td_reporter_names
UNION ALL
SELECT 'user_profiles', COUNT(*) FROM user_profiles
UNION ALL
SELECT 'user_teams', COUNT(*) FROM user_teams;

-- 2. Verify critical production data exists
SELECT key,
       CASE
           WHEN value IS NOT NULL THEN 'EXISTS'
           ELSE 'MISSING'
       END as status
FROM local_storage_data
WHERE key IN ('productionData', 'historyData', 'equipmentOptionsData');

-- 3. Check RLS policies are active
SELECT schemaname, tablename, policyname, cmd
FROM pg_policies
WHERE schemaname = 'public'
ORDER BY tablename;

-- 4. Verify indexes exist
SELECT schemaname, tablename, indexname
FROM pg_indexes
WHERE schemaname = 'public'
ORDER BY tablename;
```

## 🚨 Troubleshooting

### Common Issues & Solutions

#### 1. UUID Function Missing
```sql
-- If gen_random_uuid() doesn't exist, create it:
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
-- Then use uuid_generate_v4() instead of gen_random_uuid()
```

#### 2. Auth Schema Missing
```sql
-- If migrating without Supabase auth, remove foreign key constraints:
ALTER TABLE authorized_managers DROP CONSTRAINT IF EXISTS authorized_managers_user_id_fkey;
ALTER TABLE todos DROP CONSTRAINT IF EXISTS todos_completed_by_fkey;
ALTER TABLE todos DROP CONSTRAINT IF EXISTS todos_created_by_fkey;
ALTER TABLE notifications DROP CONSTRAINT IF EXISTS notifications_user_id_fkey;
ALTER TABLE user_profiles DROP CONSTRAINT IF EXISTS user_profiles_user_id_fkey;
ALTER TABLE user_teams DROP CONSTRAINT IF EXISTS user_teams_user_id_fkey;

-- Update RLS policies to use different auth mechanism
```

#### 3. Array Type Issues (MySQL/SQL Server)
```sql
-- Convert TEXT[] to JSON for todos.assigned_teams and todos.images
UPDATE todos SET
    assigned_teams = CASE
        WHEN assigned_teams IS NOT NULL
        THEN JSON_ARRAY(assigned_teams)
        ELSE NULL
    END,
    images = CASE
        WHEN images IS NOT NULL
        THEN JSON_ARRAY(images)
        ELSE NULL
    END;
```

#### 4. Timezone Issues
```sql
-- Convert TIMESTAMPTZ to appropriate format for target database
-- For MySQL: Convert to TIMESTAMP and handle timezone in application
-- For SQL Server: Convert to DATETIME2 and handle timezone in application
```

## 📝 Migration Checklist

- [ ] **Backup created successfully**
- [ ] **New database/server prepared**
- [ ] **Schema migrated (tables, indexes, constraints)**
- [ ] **Data migrated and verified**
- [ ] **RLS policies configured (or app-level security implemented)**
- [ ] **Storage bucket created and files migrated**
- [ ] **Environment variables updated**
- [ ] **Application tested with new database**
- [ ] **Authentication system working**
- [ ] **Production data accessible**
- [ ] **Todo system functional**
- [ ] **Logbook system operational**
- [ ] **Performance acceptable**

## 🎯 Success Criteria

Your migration is successful when:
1. ✅ All tables exist with correct schema
2. ✅ All data migrated without loss
3. ✅ Application connects and functions normally
4. ✅ Users can authenticate and access their data
5. ✅ Production data displays correctly
6. ✅ Todo system works with proper permissions
7. ✅ File uploads/downloads work (if applicable)
8. ✅ Performance meets requirements

---

**⚠️ Important Notes:**
- Always test migration on a copy/staging environment first
- Keep original database until migration is fully verified
- Update DNS/connection strings only after successful testing
- Monitor application logs for any auth/permission issues
- Have rollback plan ready in case of issues
