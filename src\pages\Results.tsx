import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { useIsMobile } from '@/hooks/use-mobile';
import { useProduction } from '@/context/ProductionContext';
import {
  Bar,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ComposedChart,
  Area,
  Line, // Added Line import
  ReferenceLine,
  BarChart,
} from 'recharts';
import { formatNumber, getLineColor, calculateEfficiencyPercent, calculateChartMaximum } from '@/utils/chartUtils';
import LineSelector from '@/components/common/LineSelector';
import { ProductionLine, Disruption, EquipmentEntry, DisruptionInput } from '@/types'; // Added DisruptionInput
import { Clock3, Percent, TrendingUp, Workflow, Settings, Activity, Target, X } from 'lucide-react';
import { loadItem } from '@/lib/local-storage';
import { Progress } from "@/components/ui/progress";
// import Logbook from '@/components/dashboard/Logbook'; // Removed Logbook import

// Map area codes to descriptive names
const areaNames: Record<string, string> = {
  "none": "Geen",
  "2-010": "Feeding & Initial Sorting",
  "2-020": "Sorting & Detection",
  "2-030": "Washing & Grinding",
  "2-060": "Hot Wash & Separation",
  "2-080": "Filtration & Belt Filters",
  "2-090": "Flake Sorting",
  "2-100": "Storage & Packaging"
};

const Dashboard: React.FC = () => {
  const {
    productionData,
    TARGETS,
    YIELD_TARGETS,
    equipmentOptions,
    independentDisruptions // Get independent disruptions state
  } = useProduction();
  const [selectedLine, setSelectedLine] = useState<ProductionLine>('tl1');
  const [selectedMetric, setSelectedMetric] = useState<'production' | 'yield'>('production');
  const [isChartFullscreen, setIsChartFullscreen] = useState(false);
  const isMobile = useIsMobile();

  const lineData = productionData[selectedLine];

  // Targets
  const currentYieldTarget = YIELD_TARGETS[selectedLine] || 0;
  const currentShiftTargetValue = TARGETS[selectedLine] || 0;
  const currentDailyTarget = currentShiftTargetValue * 3;
  const currentShiftTargetForChart = Math.round(currentShiftTargetValue);

  // Calculate total actual production for the period
  const totalProductionForPeriod = useMemo(() => {
    if (!lineData || lineData.rows.length === 0) return 0;
    return lineData.rows.reduce((total, row) => {
       return total +
         (Number(row.od.production) || 0) +
         (Number(row.md.production) || 0) +
         (Number(row.nd.production) || 0);
    }, 0);
  }, [lineData]);

  // --- Original Target Calculation (Use DAILY target) ---
  const totalTargetForPeriod = useMemo(() => {
    // Use daily target based on number of days with data
    const numberOfDays = lineData?.rows?.length || 0;
    return currentDailyTarget * numberOfDays;
  }, [currentDailyTarget, lineData?.rows?.length]);

  // --- Original Efficiency Calculation (based on DAILY target) ---
  const originalEfficiencyPercent = useMemo(() => {
     if (totalTargetForPeriod === 0) return 0;
     return (totalProductionForPeriod / totalTargetForPeriod) * 100;
  }, [totalProductionForPeriod, totalTargetForPeriod]);

  // Bereken gemiddelde yield over alle shifts in de periode (beter dan alleen laatste dag)
  const averageYield = useMemo(() => {
    if (!lineData || lineData.rows.length === 0) return 0;
    let totalYieldSum = 0;
    let validShiftCount = 0;
    lineData.rows.forEach(row => {
      const yields = [Number(row.od.yield) || 0, Number(row.md.yield) || 0, Number(row.nd.yield) || 0];
      yields.forEach(y => {
        if (y > 0) { // Tel alleen shifts mee met een yield > 0
          totalYieldSum += y;
          validShiftCount++;
        }
      });
    });
    return validShiftCount === 0 ? 0 : totalYieldSum / validShiftCount;
  }, [lineData]);

  // --- Final Corrected Efficiency Calculation ---
  const averageShiftEfficiency = useMemo(() => {
    // Formula: (Actual Production / Target Production) * (Achieved Yield % / Target Yield %) * 100
    if (totalTargetForPeriod === 0 || currentYieldTarget === 0 || averageYield === undefined) {
      // Avoid division by zero or using undefined yield
      return 0;
    }

    const productionRatio = totalProductionForPeriod / totalTargetForPeriod;
    // averageYield and currentYieldTarget are already percentages (e.g., 57.5)
    const yieldRatio = averageYield / currentYieldTarget;

    const finalEfficiency = productionRatio * yieldRatio * 100; // Multiply by 100 to get percentage

    return finalEfficiency;

  }, [totalProductionForPeriod, totalTargetForPeriod, averageYield, currentYieldTarget]);


  const [maxYValue, setMaxYValue] = useState<number>(0);
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const [chartHeight, setChartHeight] = useState(500);

  // Maximum Y-axis values per production line
  const MAX_Y_VALUES: Record<ProductionLine, number> = {
    tl1: 25000,
    tl2: 35000,
    p1: 15000,
    p2: 25000,
    p3: 40000,
    Yard: 10000,
    Koeling: 5000,
    Gebouw: 5000,
    Overige: 5000
  };

  // Update chart height based on container width to maintain aspect ratio
  useEffect(() => {
    const updateDimensions = () => {
      if (chartContainerRef.current) {
        const width = chartContainerRef.current.offsetWidth;
        setChartHeight(Math.max(Math.floor(width * 0.45), 400));
      }
    };
    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => { window.removeEventListener('resize', updateDimensions); };
  }, []);

  useEffect(() => {
    // Use MAX_Y_VALUES directly instead of relying on currentRow
    const maxYValue = MAX_Y_VALUES[selectedLine];
    setMaxYValue(maxYValue);
  }, [selectedLine]);

  const toggleChartFullscreen = useCallback(async () => {
    if (isMobile) {
      const nextFullscreenState = !isChartFullscreen;
      try {
        if (nextFullscreenState) {
          // Attempt to lock to landscape when entering fullscreen
          console.log('Attempting to lock orientation to landscape...');
          await screen.orientation.lock('landscape-primary');
          console.log('Orientation lock successful.');
        } else {
          // Unlock orientation when exiting fullscreen
          console.log('Unlocking orientation...');
          screen.orientation.unlock();
          console.log('Orientation unlocked.');
        }
      } catch (error) {
        console.error("Screen orientation lock/unlock failed:", error);
        // Log the error for debugging on the device
      }
      // Update the state regardless of orientation success/failure
      setIsChartFullscreen(nextFullscreenState);
    }
  }, [isMobile, isChartFullscreen]);

  // --- Hulpfuncties (Hooks) HIER plaatsen ---
  const getEquipmentLabel = useCallback((value: string | undefined | null) => {
    if (!value || !equipmentOptions || !equipmentOptions[selectedLine]) return value || '-';
    for (const area in equipmentOptions[selectedLine]) {
      const found = equipmentOptions[selectedLine][area]?.find((option) => option.value === value);
      if (found) return found.label_nl;
    }
    return value;
  }, [equipmentOptions, selectedLine]);



  const getTextColorClass = (value: number, target: number): string => {
    if (target === 0) return 'text-gray-500';
    // Adjust target comparison for efficiency percentage (target is 100%)
    // Or adjust based on the specific target for the value being compared
    // For averageShiftEfficiency, the implicit target is 100%
    if (target === 100) { // Assuming target is 100 for efficiency
        return value >= 100 ? 'text-green-500' : 'text-red-500';
    }
    // Default comparison for other values like production
    return value >= target ? 'text-green-500' : 'text-red-500';
  };


  // Component for colored text in tooltip
  const ColoredTooltipText: React.FC<{ color: string; children: React.ReactNode }> = ({ color, children }) => {
    return (
      <p
        className="font-medium tooltip-colored-text"
        style={{ '--tooltip-color': color } as React.CSSProperties}
      >
        {children}
      </p>
    );
  };

  // Define CustomTooltip function for Recharts chart
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white/90 backdrop-blur-sm p-3 rounded-lg shadow-md border border-gray-100 text-sm">
          <p className="font-medium mb-1">{label.split('\n')[0]}</p>
          <p className="text-xs text-gray-500 mb-2">{label.split('\n')[1]}</p>
          {payload.map((pld: any, index: number) => (
            <ColoredTooltipText key={index} color={pld.color}>
              {pld.name === 'productie' ? 'Productie' : pld.name === 'yield' ? 'Yield' : pld.name}:
              <span className="ml-1">
                 {pld.name === 'yield' ? `${pld.value.toFixed(1)}%` : pld.value.toLocaleString('nl-NL')}
              </span>
            </ColoredTooltipText>
          ))}
        </div>
      );
    }
    return null;
  };



  // Calculate Top 3 Independent Disruptions by Duration
  const top3IndependentDisruptions = useMemo(() => {
    const parseDuration = (duration: string | undefined): number => {
      if (!duration) return 0;
      const parts = duration.split(':');
      if (parts.length !== 2) return 0;
      const hours = parseInt(parts[0], 10) || 0;
      const minutes = parseInt(parts[1], 10) || 0;
      return hours * 60 + minutes;
    };

    return (independentDisruptions?.[selectedLine] || [])
      .filter((d): d is DisruptionInput => d !== null && !!d.duration && d.duration !== '00:00') // Filter non-null with valid duration
      .map(d => ({ ...d, durationMinutes: parseDuration(d.duration) })) // Add duration in minutes
      .sort((a, b) => b.durationMinutes - a.durationMinutes) // Sort descending
      .slice(0, 3); // Take top 3
  }, [independentDisruptions, selectedLine]);


  // --- Vroege return NU HIER ---
  if (!lineData) { // Simplified check
    return (
      <div className="flex justify-center items-center h-96">
        <p>Geen gegevens beschikbaar voor lijn {selectedLine.toUpperCase()}</p>
      </div>
    );
  }
  // --- Einde Vroege return ---

  const chartData = (lineData?.rows || []).flatMap(row => [ // Added safe navigation
    { name: `${row.date}, OD\n${row.od.material || ''}`, productie: Number(row.od.production), yield: parseFloat(String(row.od.yield)), target: currentShiftTargetForChart, material: row.od.material || "" },
    { name: `${row.date}, MD\n${row.md.material || ''}`, productie: Number(row.md.production), yield: parseFloat(String(row.md.yield)), target: currentShiftTargetForChart, material: row.md.material || "" },
    { name: `${row.date}, ND\n${row.nd.material || ''}`, productie: Number(row.nd.production), yield: parseFloat(String(row.nd.yield)), target: currentShiftTargetForChart, material: row.nd.material || "" }
  ]);

  const allShifts = (lineData?.rows || []).flatMap((row) => [ Number(row.od.production), Number(row.md.production), Number(row.nd.production) ]);
  const nonZeroShifts = allShifts.filter((shift) => shift > 0);
  const averageProduction = nonZeroShifts.length ? Math.round(nonZeroShifts.reduce((sum, current) => sum + current, 0) / nonZeroShifts.length) : 0;

  const odShifts = (lineData?.rows || []).map((row) => Number(row.od.production)).filter((shift) => shift > 0);
  const mdShifts = (lineData?.rows || []).map((row) => Number(row.md.production)).filter((shift) => shift > 0);
  const ndShifts = (lineData?.rows || []).map((row) => Number(row.nd.production)).filter((shift) => shift > 0);

  const averageOd = odShifts.length ? Math.round(odShifts.reduce((sum, current) => sum + current, 0) / odShifts.length) : 0;
  const averageMd = mdShifts.length ? Math.round(mdShifts.reduce((sum, current) => sum + current, 0) / mdShifts.length) : 0;
  const averageNd = ndShifts.length ? Math.round(ndShifts.reduce((sum, current) => sum + current, 0) / ndShifts.length) : 0;


  return (
    <div className="animate-fade-in bg-gray-50/70 rounded-lg backdrop-blur-sm p-4 md:p-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        {/* Dashboard heading removed */}
        {/* LineSelector moved below */}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* Card 1: Target Behaald % */}
        <div className="dashboard-card p-4 hover:scale-102 transition-transform border border-gray-100">
          <div className="flex justify-between items-start">
            <div>
              {/* Content swapped from Card 3 */}
              <p className="text-gray-500 text-sm">Totaal productie</p>
              <p className={`text-2xl font-bold ${getTextColorClass(totalProductionForPeriod, totalTargetForPeriod)}`}>
                {formatNumber(totalProductionForPeriod)}
              </p>
            </div>
            {/* Icon removed */}
          </div>
          <div className="mt-2 pt-2">
            <Progress value={Math.min(originalEfficiencyPercent, 100)} className="h-2 mb-1" />
            <div className="flex justify-between items-center mt-1">
              <p className="text-sm">Doel (Prod): <span className="font-medium">{formatNumber(totalTargetForPeriod)}</span></p>
            </div>
          </div>
        </div>

        {/* Card 2: Gemiddelde Yield % */}
        <div className="dashboard-card p-4 hover:scale-102 transition-transform border border-gray-100">
          <div className="flex justify-between">
            <div>
              <p className="text-gray-500 text-sm">Gemiddelde Yield %</p>
              <p className={`text-2xl font-bold ${averageYield >= currentYieldTarget ? 'text-green-500' : 'text-red-500'}`}>
                {averageYield.toFixed(1).replace('.', ',')}%
              </p>
            </div>
            {/* Icon removed */}
          </div>
          <div className="mt-2 pt-2">
            <Progress value={currentYieldTarget > 0 ? (averageYield / currentYieldTarget) * 100 : 0} className="h-2 mb-1" />
             <div className="flex justify-between items-center mt-1">
               <p className="text-sm">Doel (Yield): <span className="font-medium">{currentYieldTarget.toFixed(1)}%</span></p>
             </div>
          </div>
        </div>

        {/* Card 3: Totaal productie & Shift Eff. */}
        <div className="dashboard-card p-4 hover:scale-102 transition-transform border border-gray-100">
          <div className="flex justify-between">
            <div>
              {/* Content swapped from Card 1 */}
              <p className="text-gray-500 text-sm">Target Behaald %</p>
              <p className={`text-2xl font-bold ${originalEfficiencyPercent >= 100 ? 'text-green-500' : 'text-red-500'}`}>
                {originalEfficiencyPercent.toFixed(1).replace('.', ',')}%
              </p>
            </div>
            {/* Icon removed */}
          </div>
          <div className="mt-4 pt-4 border-t border-gray-100">
            <div className="flex justify-between items-center">
              <p className="text-sm">Efficiëntie: {' '}
                <span
                  // Displaying the result of the FINAL CORRECTED formula. Formatting as percentage.
                  // Update the tooltip for the FINAL CORRECTED calculation.
                  className={`font-medium ${averageShiftEfficiency >= 100 ? 'text-green-500' : 'text-red-500'} cursor-help underline decoration-dotted decoration-gray-400`} // Standard efficiency color logic
                  title={`Efficiëntie = (Werkelijke Prod / Target Prod) * (Behaalde Yield % / Target Yield %) = (${formatNumber(totalProductionForPeriod)} / ${formatNumber(totalTargetForPeriod)}) * (${averageYield.toFixed(1)}% / ${currentYieldTarget.toFixed(1)}%)`}
                >
                  {averageShiftEfficiency.toFixed(2).replace('.', ',')}% {/* Display as percentage */}
                </span>
              </p>
            </div>
          </div>
        </div>

        {/* Card 4: Gemiddelde per shift */}
        <div className="dashboard-card p-4 hover:scale-102 transition-transform border border-gray-100">
          <div className="flex justify-between">
            <div>
              <p className="text-gray-500 text-sm">Gemiddelde per shift</p>
              <p className="text-2xl font-bold">{formatNumber(averageProduction)}</p>
            </div>
            {/* Icon removed */}
          </div>
          <div className="mt-4 pt-4 border-t border-gray-100 text-xs">
            <div className="grid grid-cols-3 gap-2">
              <div> <p className="text-gray-500">OD</p> <p className={`font-medium ${getTextColorClass(averageOd, currentShiftTargetValue)}`}>{formatNumber(averageOd)}</p> </div>
              <div> <p className="text-gray-500">MD</p> <p className={`font-medium ${getTextColorClass(averageMd, currentShiftTargetValue)}`}>{formatNumber(averageMd)}</p> </div>
              <div> <p className="text-gray-500">ND</p> <p className={`font-medium ${getTextColorClass(averageNd, currentShiftTargetValue)}`}>{formatNumber(averageNd)}</p> </div>
            </div>
          </div>
        </div>
      </div>

      {/* Chart Section */}
      <div className="mb-6">
        <div className="dashboard-card overflow-hidden hover:shadow-lg transition-shadow border border-gray-100">
          <div className="p-4 md:p-5 border-b border-gray-100">
            {/* Productie & Yield Trend heading removed */}
            {/* Inserted Line Selector Buttons Div */}
            {/* Removed static button group */}
            {/* Line Selector Component */}
            <LineSelector
              selectedLine={selectedLine}
              onChange={setSelectedLine}
              className="mt-2"
            />
          </div>
          <div
            ref={chartContainerRef}
            style={isChartFullscreen ? { height: '100vh', width: '100vw' } : { height: `${chartHeight}px` }}
            className={`relative p-2 transition-all duration-300 ease-in-out ${isChartFullscreen ? 'fixed inset-0 z-50 bg-white' : ''}`}
            onClick={isMobile && !isChartFullscreen ? toggleChartFullscreen : undefined} // Only trigger fullscreen on tap when not already fullscreen
          >
            {/* Fullscreen Close Button */}
            {isChartFullscreen && (
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation(); // Prevent chart click handler
                  toggleChartFullscreen();
                }}
                className="absolute top-4 right-4 z-[9999] p-2 bg-gray-600 text-white rounded-full shadow-lg hover:bg-gray-800 transition-colors"
                aria-label="Close fullscreen"
              >
                <X className="h-5 w-5" />
              </button>
            )}
            <ResponsiveContainer width="100%" height="100%">
              <ComposedChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
                <CartesianGrid strokeDasharray="3 3" vertical={false} strokeOpacity={0.5} />
                <XAxis dataKey="name" angle={-45} textAnchor="end" height={120} interval={0} tick={(props) => {
                    const { x, y, payload } = props; const parts = payload.value.split('\n');
                    return (
                      <g transform={`translate(${x},${y})`}>
                        <text x={0} y={0} dy={0} textAnchor="end" fill="#666" transform="rotate(-45)" style={{ fontSize: '12px', fontWeight: 'bold' }}>{parts[0]}</text>
                        {parts[1] && (<text x={0} y={0} dy={14} textAnchor="end" fill="#666" transform="rotate(-45)" style={{ fontSize: '12px', fontWeight: 'bold' }}>{parts[1]}</text>)}
                      </g>
                    );
                  }} />
                <YAxis yAxisId="left" orientation="left" domain={[0, MAX_Y_VALUES[selectedLine]]} label={{ value: 'Productie', angle: -90, position: 'insideLeft', offset: -10, style: { fill: '#0369a1' } }} tick={{ style: { fontSize: 16 } }} tickFormatter={(value) => value.toString().replace('.', ',')} />
                <YAxis yAxisId="right" orientation="right" domain={[0, 100]} label={{ value: 'Yield (%)', angle: 90, position: 'insideRight', style: { fill: '#8b5cf6' } }} tick={{ style: { fontSize: 16 } }} tickFormatter={(value) => Number(value).toFixed(1).toString().replace('.', ',')} />
                <Tooltip content={<CustomTooltip />} />
                <Legend verticalAlign="top" height={36} />
                <style>{`.recharts-legend-item { margin-left: 20px !important; }`}</style>
                <ReferenceLine y={currentShiftTargetForChart} yAxisId="left" label={{ value: `Target: ${formatNumber(currentShiftTargetForChart)}`, position: 'insideTopRight', fill: '#8b5cf6', fontSize: 12 }} stroke="#6b7280" strokeDasharray="3 3" />
                {/* Ensured Yield target line and label are purple */}
                <ReferenceLine y={currentYieldTarget} yAxisId="right" label={{ value: `Target: ${currentYieldTarget.toFixed(1)}%`, position: 'insideTopLeft', fill: '#8b5cf6', fontSize: 12 }} stroke="#8b5cf6" strokeDasharray="3 3" />
                <defs>
                  <linearGradient id="colorProductie" x1="0" y1="1" x2="0" y2="0">
                    <stop offset="5%" stopColor={getLineColor(selectedLine)} stopOpacity={0.1}/>
                    <stop offset="95%" stopColor={getLineColor(selectedLine)} stopOpacity={0.6}/>
                  </linearGradient>
                  {/* Removed old colorYield gradient */}
                  {/* Define the new vertical red-to-green gradient for yield bars */}
                  <linearGradient id="yieldBarGradient" x1="0" y1="1" x2="0" y2="0">
                    <stop offset="0%" stopColor="#8b5cf6" /> {/* Purple at bottom */}
                    <stop offset="100%" stopColor="#22c55e" /> {/* Green at top */}
                  </linearGradient>
                </defs>
                {/* Bar rendered first */}
                <Bar dataKey="yield" yAxisId="right" name="Yield" maxBarSize={20} stroke="#000000" strokeWidth={1}>
                  {chartData.map((entry, index) => {
                    const isAboveTarget = entry.yield >= currentYieldTarget;
                    // Use solid green if target met/exceeded, otherwise use the vertical gradient
                    const fillColor = isAboveTarget ? '#22c55e' : 'url(#yieldBarGradient)';
                    return <Cell key={`cell-yield-${index}`} fill={fillColor} />;
                  })}
                </Bar>
                {/* Area rendered second (on top) */}
                <Area type="monotone" dataKey="productie" yAxisId="left" stroke={getLineColor(selectedLine)} fillOpacity={1} fill="url(#colorProductie)" strokeWidth={2} name="Productie"
                  dot={({ cx, cy, payload, index }) => {
                    const isAboveTarget = payload.productie >= currentShiftTargetForChart;
                    const color = isAboveTarget ? '#22c55e' : '#ef4444'; // green-500 : red-500
                    return (
                      // Increased radius from 5 to 7
                      <circle key={`dot-prod-${index}`} cx={cx} cy={cy} r={7} fill={color} stroke="#fff" strokeWidth={1} />
                    );
                  }}
                  activeDot={({ cx, cy, payload, index }) => {
                    const isAboveTarget = payload.productie >= currentShiftTargetForChart;
                    const color = isAboveTarget ? '#22c55e' : '#ef4444'; // green-500 : red-500
                    return (
                      // Increased radius from 7 to 9
                      <circle key={`active-dot-prod-${index}`} cx={cx} cy={cy} r={9} fill={color} stroke="#fff" strokeWidth={2} />
                    );
                  }}
                />
              </ComposedChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* REMOVED Logbook section from Dashboard */}

      {/* Top 3 Independent Disruptions Display Section */}
      <div className="dashboard-card p-4 mt-6 border border-gray-100">
        <h2 className="text-lg font-semibold mb-4 border-b pb-2">Top 3 Storingen / Downtime (Total Downtime)</h2>
        {(() => {
          // Use top3IndependentDisruptions calculated earlier
          if (top3IndependentDisruptions.length === 0) {
            return <p className="text-sm text-gray-500 italic">Geen storingen met duur ingevoerd.</p>;
          }
          return (
            <div className="space-y-3"> {/* Removed max-h and overflow */}
              {top3IndependentDisruptions.map((disruption, index) => (
                <div key={`${selectedLine}-${index}-top3`} className="flex justify-between items-start text-xs border-b pb-2 last:border-b-0">
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-gray-800">{getEquipmentLabel(disruption.equipment)}</span>
                      {disruption.duration && disruption.duration !== '00:00' && (
                        <div className="inline-flex items-center rounded-full border px-2 py-0.5 text-[10px] font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-destructive/10 text-destructive">
                          {disruption.duration}
                        </div>
                      )}
                    </div>
                    <p className="text-gray-600 mt-1 text-xs">{disruption.description || '-'}</p>
                  </div>
                  {/* Optional: Add timestamp if available in DisruptionInput */}
                </div>
              ))}
            </div>
          );
        })()}
      </div>
    </div>
  );
};

export default Dashboard;